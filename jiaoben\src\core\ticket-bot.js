/**
 * Galaxy Ticket Bot - 核心购票机器人
 * 实现购票流程的自动化逻辑
 */

import { config } from '../config/config.js';
import logger from '../utils/logger.js';
import BrowserManager from './browser-manager.js';

/**
 * 购票机器人类
 */
export class TicketBot {
  constructor() {
    this.browserManager = new BrowserManager();
    this.isRunning = false;
    this.currentSession = null;
    this.retryCount = 0;
    this.maxRetries = config.app.maxRetries;
    this.startTime = null;
  }

  /**
   * 启动购票流程
   */
  async start(ticketConfig = {}) {
    if (this.isRunning) {
      logger.ticketError('购票流程已在运行中');
      return false;
    }

    try {
      this.isRunning = true;
      this.startTime = Date.now();
      this.retryCount = 0;

      logger.ticketInfo('🚀 开始购票流程', {
        targetUrl: config.ticket.targetUrl,
        quantity: config.ticket.quantity,
        maxRetries: this.maxRetries,
      });

      // 初始化浏览器
      await this.browserManager.initialize();

      // 创建购票会话
      this.currentSession = await this.createSession();

      // 执行购票流程
      const result = await this.executeTicketingFlow(ticketConfig);

      if (result.success) {
        logger.ticketSuccess('🎉 购票流程完成', {
          duration: Date.now() - this.startTime,
          result: result.data,
        });
      } else {
        logger.ticketError('❌ 购票流程失败', {
          duration: Date.now() - this.startTime,
          error: result.error,
        });
      }

      return result;

    } catch (error) {
      logger.ticketError('购票流程异常', { error: error.message });
      return { success: false, error: error.message };
    } finally {
      this.isRunning = false;
      await this.cleanup();
    }
  }

  /**
   * 创建购票会话
   */
  async createSession() {
    logger.step(1, 7, '创建购票会话');

    const { page, pageId } = await this.browserManager.createPage('ticket-session');
    
    const session = {
      page,
      pageId,
      contextId: 'ticket-session',
      startTime: Date.now(),
    };

    logger.ticketInfo('购票会话创建成功', { pageId });
    return session;
  }

  /**
   * 执行购票流程
   */
  async executeTicketingFlow(ticketConfig) {
    const steps = [
      { name: '访问网站', method: 'navigateToSite' },
      { name: '分析页面', method: 'analyzePage' },
      { name: '检查登录状态', method: 'checkLoginStatus' },
      { name: '选择活动', method: 'selectEvent' },
      { name: '选择票务', method: 'selectTickets' },
      { name: '填写信息', method: 'fillUserInfo' },
      { name: '确认订单', method: 'confirmOrder' },
    ];

    let currentStep = 0;
    const totalSteps = steps.length;

    try {
      for (const step of steps) {
        currentStep++;
        logger.step(currentStep, totalSteps, step.name);

        const stepResult = await this[step.method](ticketConfig);
        
        if (!stepResult.success) {
          // 如果步骤失败，尝试重试
          if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            logger.ticketInfo(`步骤失败，准备重试 (${this.retryCount}/${this.maxRetries})`, {
              step: step.name,
              error: stepResult.error,
            });
            
            // 等待一段时间后重试
            await this.wait(2000);
            return await this.executeTicketingFlow(ticketConfig);
          } else {
            return { success: false, error: `步骤失败: ${step.name} - ${stepResult.error}` };
          }
        }

        // 步骤间等待
        await this.wait(config.advanced.pageLoadWait);
      }

      return { success: true, data: { message: '购票流程完成，请手动完成支付' } };

    } catch (error) {
      logger.ticketError('购票流程执行异常', { error: error.message, step: currentStep });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤1: 访问网站
   */
  async navigateToSite(ticketConfig) {
    try {
      const { page } = this.currentSession;
      const targetUrl = ticketConfig.url || config.ticket.targetUrl;

      logger.ticketInfo('正在访问目标网站', { url: targetUrl });

      await page.goto(targetUrl, {
        waitUntil: config.waitStrategies.networkIdle,
        timeout: config.app.timeout,
      });

      // 等待页面加载完成
      await page.waitForLoadState('domcontentloaded');

      // 检查页面是否正常加载
      const title = await page.title();
      logger.ticketInfo('页面加载成功', { title, url: page.url() });

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/01-homepage.png');
      }

      return { success: true };
    } catch (error) {
      logger.ticketError('访问网站失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤2: 分析页面
   */
  async analyzePage(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在分析页面结构');

      // 检查常见元素
      const pageAnalysis = await page.evaluate(() => {
        return {
          hasEventList: !!document.querySelector('.event-list, .events, .show-list'),
          hasSearchBox: !!document.querySelector('input[type="search"], .search-input'),
          hasDatePicker: !!document.querySelector('.date-picker, input[type="date"]'),
          hasLoginButton: !!document.querySelector('.login, .sign-in, .user-login, a[href*="login"]'),
          hasCartButton: !!document.querySelector('.cart, .shopping-cart'),
          hasUserInfo: !!document.querySelector('.user-info, .user-name, .logged-in'),
          title: document.title,
          url: window.location.href,
        };
      });

      logger.ticketInfo('页面分析完成', pageAnalysis);

      return { success: true, data: pageAnalysis };
    } catch (error) {
      logger.ticketError('页面分析失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤3: 检查登录状态
   */
  async checkLoginStatus(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在检查登录状态');

      // 检查登录状态
      const loginStatus = await page.evaluate(() => {
        // 检查是否有用户信息显示
        const hasUserInfo = !!document.querySelector('.user-info, .user-name, .logged-in, .user-avatar');

        // 检查是否有登录按钮
        const hasLoginButton = !!document.querySelector('.login, .sign-in, .user-login, a[href*="login"]');

        // 检查URL是否包含登录相关信息
        const urlHasLogin = window.location.href.includes('login');

        // 检查是否有登出按钮
        const hasLogoutButton = !!document.querySelector('.logout, .sign-out, a[href*="logout"]');

        return {
          isLoggedIn: hasUserInfo || hasLogoutButton,
          needsLogin: hasLoginButton && !hasUserInfo,
          isLoginPage: urlHasLogin,
          hasUserInfo,
          hasLoginButton,
          hasLogoutButton,
        };
      });

      logger.ticketInfo('登录状态检查完成', loginStatus);

      // 如果需要登录，等待用户手动登录
      if (loginStatus.needsLogin || loginStatus.isLoginPage) {
        logger.ticketInfo('⚠️ 检测到需要登录，请在浏览器中手动登录');

        // 等待用户登录
        await this.waitForUserLogin(page);
      }

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/02-login-check.png');
      }

      return { success: true, data: loginStatus };
    } catch (error) {
      logger.ticketError('登录状态检查失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 等待用户手动登录
   */
  async waitForUserLogin(page) {
    logger.ticketInfo('🔐 等待用户登录...');
    logger.ticketInfo('请在浏览器中完成登录操作，程序将自动检测登录状态');

    const maxWaitTime = 300000; // 5分钟
    const checkInterval = 3000; // 3秒检查一次
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        // 检查登录状态
        const isLoggedIn = await page.evaluate(() => {
          // 多种方式检测登录状态
          const hasUserInfo = !!document.querySelector('.user-info, .user-name, .logged-in, .user-avatar');
          const hasLogoutButton = !!document.querySelector('.logout, .sign-out, a[href*="logout"]');
          const noLoginButton = !document.querySelector('.login, .sign-in, .user-login, a[href*="login"]');

          // 检查URL变化
          const urlChanged = !window.location.href.includes('login');

          return hasUserInfo || hasLogoutButton || (noLoginButton && urlChanged);
        });

        if (isLoggedIn) {
          logger.ticketSuccess('✅ 检测到用户已登录，继续购票流程');
          return;
        }

        // 显示等待进度
        const remainingTime = Math.ceil((maxWaitTime - waitTime) / 1000);
        logger.ticketInfo(`⏳ 等待登录中... (剩余 ${remainingTime} 秒)`);

        await this.wait(checkInterval);
        waitTime += checkInterval;

      } catch (error) {
        logger.ticketError('登录状态检测异常', { error: error.message });
        await this.wait(checkInterval);
        waitTime += checkInterval;
      }
    }

    // 超时处理
    logger.ticketError('⏰ 等待登录超时，请检查登录状态');
    throw new Error('等待用户登录超时');
  }

  /**
   * 步骤4: 选择活动
   */
  async selectEvent(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在选择活动');

      // 查找活动列表
      const eventSelectors = [
        config.selectors.eventList,
        '.event-item',
        '.show-item',
        '.performance-item',
        'a[href*="event"]',
        'a[href*="show"]',
      ];

      let eventFound = false;
      for (const selector of eventSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          
          // 点击第一个活动（或根据配置选择特定活动）
          if (ticketConfig.eventName) {
            // 根据活动名称选择
            const eventElement = await page.locator(selector)
              .filter({ hasText: ticketConfig.eventName })
              .first();
            
            if (await eventElement.count() > 0) {
              await eventElement.click();
              eventFound = true;
              logger.ticketInfo('已选择指定活动', { eventName: ticketConfig.eventName });
              break;
            }
          } else {
            // 选择第一个活动
            await page.locator(selector).first().click();
            eventFound = true;
            logger.ticketInfo('已选择第一个活动');
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!eventFound) {
        return { success: false, error: '未找到可选择的活动' };
      }

      // 等待页面跳转
      await page.waitForLoadState('domcontentloaded');

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/03-event-selected.png');
      }

      return { success: true };
    } catch (error) {
      logger.ticketError('选择活动失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤5: 选择票务
   */
  async selectTickets(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在选择票务');

      // 查找票务选择区域
      const ticketSelectors = [
        '.ticket-selection',
        '.seat-selection',
        '.price-selection',
        'select[name*="ticket"]',
        'input[name*="quantity"]',
      ];

      // 设置票数
      const quantity = ticketConfig.quantity || config.ticket.quantity;
      
      // 尝试不同的票数设置方式
      const quantityMethods = [
        // 方法1: 直接输入框
        async () => {
          const quantityInput = page.locator(config.selectors.quantityInput).first();
          if (await quantityInput.count() > 0) {
            await quantityInput.fill(quantity.toString());
            return true;
          }
          return false;
        },
        
        // 方法2: 点击增加按钮
        async () => {
          const increaseBtn = page.locator(config.selectors.quantityIncrease).first();
          if (await increaseBtn.count() > 0) {
            for (let i = 1; i < quantity; i++) {
              await increaseBtn.click();
              await this.wait(500);
            }
            return true;
          }
          return false;
        },

        // 方法3: 下拉选择
        async () => {
          const selectElement = page.locator('select').filter({ hasText: /数量|quantity/i }).first();
          if (await selectElement.count() > 0) {
            await selectElement.selectOption(quantity.toString());
            return true;
          }
          return false;
        },
      ];

      let quantitySet = false;
      for (const method of quantityMethods) {
        try {
          if (await method()) {
            quantitySet = true;
            logger.ticketInfo('票数设置成功', { quantity });
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!quantitySet) {
        logger.ticketInfo('未找到票数设置选项，使用默认值');
      }

      // 查找并点击选择/购买按钮
      const selectButtons = [
        config.selectors.selectButton,
        '.buy-now',
        '.add-to-cart',
        '.book-now',
        'button:has-text("选择")',
        'button:has-text("购买")',
        'button:has-text("立即购买")',
      ];

      let buttonClicked = false;
      for (const selector of selectButtons) {
        try {
          const button = page.locator(selector).first();
          if (await button.count() > 0 && await button.isVisible()) {
            await button.click();
            buttonClicked = true;
            logger.ticketInfo('已点击选择按钮');
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!buttonClicked) {
        return { success: false, error: '未找到票务选择按钮' };
      }

      // 等待页面响应
      await page.waitForLoadState('domcontentloaded');

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/04-tickets-selected.png');
      }

      return { success: true };
    } catch (error) {
      logger.ticketError('选择票务失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤6: 填写用户信息
   */
  async fillUserInfo(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在填写用户信息');

      const userInfo = {
        ...config.ticket.userInfo,
        ...ticketConfig.userInfo,
      };

      // 填写姓名
      if (userInfo.name) {
        try {
          const nameInput = page.locator(config.selectors.userNameInput).first();
          if (await nameInput.count() > 0) {
            await nameInput.fill(userInfo.name);
            logger.ticketInfo('姓名填写完成');
          }
        } catch (e) {
          logger.ticketInfo('姓名输入框未找到');
        }
      }

      // 填写邮箱
      if (userInfo.email) {
        try {
          const emailInput = page.locator(config.selectors.userEmailInput).first();
          if (await emailInput.count() > 0) {
            await emailInput.fill(userInfo.email);
            logger.ticketInfo('邮箱填写完成');
          }
        } catch (e) {
          logger.ticketInfo('邮箱输入框未找到');
        }
      }

      // 填写电话
      if (userInfo.phone) {
        try {
          const phoneInput = page.locator(config.selectors.userPhoneInput).first();
          if (await phoneInput.count() > 0) {
            await phoneInput.fill(userInfo.phone);
            logger.ticketInfo('电话填写完成');
          }
        } catch (e) {
          logger.ticketInfo('电话输入框未找到');
        }
      }

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/05-info-filled.png');
      }

      return { success: true };
    } catch (error) {
      logger.ticketError('填写用户信息失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤7: 确认订单
   */
  async confirmOrder(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在确认订单');

      // 查找确认按钮
      const confirmButtons = [
        config.selectors.submitButton,
        '.confirm-order',
        '.proceed-to-payment',
        'button:has-text("确认")',
        'button:has-text("提交")',
        'button:has-text("下一步")',
      ];

      let confirmClicked = false;
      for (const selector of confirmButtons) {
        try {
          const button = page.locator(selector).first();
          if (await button.count() > 0 && await button.isVisible()) {
            await button.click();
            confirmClicked = true;
            logger.ticketInfo('已点击确认按钮');
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!confirmClicked) {
        logger.ticketInfo('未找到确认按钮，可能已到达支付页面');
      }

      // 等待页面响应
      await page.waitForLoadState('domcontentloaded');

      // 检查是否到达支付页面
      const isPaymentPage = await page.evaluate(() => {
        const url = window.location.href.toLowerCase();
        const content = document.body.textContent.toLowerCase();
        return url.includes('payment') || 
               url.includes('checkout') || 
               content.includes('支付') || 
               content.includes('payment') ||
               content.includes('checkout');
      });

      if (isPaymentPage) {
        logger.ticketSuccess('已到达支付页面，请手动完成支付');
      }

      // 保存最终截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/06-order-confirmed.png');
      }

      return { success: true, data: { paymentRequired: isPaymentPage } };
    } catch (error) {
      logger.ticketError('确认订单失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 等待指定时间
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 停止购票流程
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    logger.ticketInfo('正在停止购票流程');
    this.isRunning = false;
    await this.cleanup();
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.currentSession) {
        await this.browserManager.closePage(this.currentSession.pageId);
        await this.browserManager.closeContext(this.currentSession.contextId);
        this.currentSession = null;
      }

      await this.browserManager.cleanup();
      logger.ticketInfo('购票资源清理完成');
    } catch (error) {
      logger.ticketError('资源清理失败', { error: error.message });
    }
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      startTime: this.startTime,
      duration: this.startTime ? Date.now() - this.startTime : 0,
      currentSession: this.currentSession ? {
        pageId: this.currentSession.pageId,
        contextId: this.currentSession.contextId,
        startTime: this.currentSession.startTime,
      } : null,
      browserStatus: this.browserManager.getStatus(),
    };
  }
}

export default TicketBot;
