/**
 * Galaxy Ticket Bot - 核心购票机器人
 * 实现购票流程的自动化逻辑
 */

import { config } from '../config/config.js';
import logger from '../utils/logger.js';
import BrowserManager from './browser-manager.js';

/**
 * 购票机器人类
 */
export class TicketBot {
  constructor() {
    this.browserManager = new BrowserManager();
    this.isRunning = false;
    this.currentSession = null;
    this.retryCount = 0;
    this.maxRetries = config.app.maxRetries;
    this.startTime = null;
  }

  /**
   * 启动购票流程
   */
  async start(ticketConfig = {}) {
    if (this.isRunning) {
      logger.ticketError('购票流程已在运行中');
      return false;
    }

    try {
      this.isRunning = true;
      this.startTime = Date.now();
      this.retryCount = 0;

      logger.ticketInfo('🚀 开始购票流程', {
        targetUrl: config.ticket.targetUrl,
        quantity: config.ticket.quantity,
        maxRetries: this.maxRetries,
      });

      // 初始化浏览器
      await this.browserManager.initialize();

      // 创建购票会话
      this.currentSession = await this.createSession();

      // 执行购票流程
      const result = await this.executeTicketingFlow(ticketConfig);

      if (result.success) {
        logger.ticketSuccess('🎉 购票流程完成', {
          duration: Date.now() - this.startTime,
          result: result.data,
        });
      } else {
        logger.ticketError('❌ 购票流程失败', {
          duration: Date.now() - this.startTime,
          error: result.error,
        });
      }

      return result;

    } catch (error) {
      logger.ticketError('购票流程异常', { error: error.message });
      return { success: false, error: error.message };
    } finally {
      this.isRunning = false;
      await this.cleanup();
    }
  }

  /**
   * 创建购票会话
   */
  async createSession() {
    logger.step(1, 7, '创建购票会话');

    const { page, pageId } = await this.browserManager.createPage('ticket-session');
    
    const session = {
      page,
      pageId,
      contextId: 'ticket-session',
      startTime: Date.now(),
    };

    logger.ticketInfo('购票会话创建成功', { pageId });
    return session;
  }

  /**
   * 执行购票流程
   */
  async executeTicketingFlow(ticketConfig) {
    const steps = [
      { name: '访问网站', method: 'navigateToSite' },
      { name: '分析页面', method: 'analyzePage' },
      { name: '检查登录状态', method: 'checkLoginStatus' },
      { name: '选择活动', method: 'selectEvent' },
      { name: '选择票务', method: 'selectTickets' },
      { name: '填写信息', method: 'fillUserInfo' },
      { name: '确认订单', method: 'confirmOrder' },
    ];

    let currentStep = 0;
    const totalSteps = steps.length;

    try {
      for (const step of steps) {
        currentStep++;
        logger.step(currentStep, totalSteps, step.name);

        const stepResult = await this[step.method](ticketConfig);
        
        if (!stepResult.success) {
          // 如果步骤失败，尝试重试
          if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            logger.ticketInfo(`步骤失败，准备重试 (${this.retryCount}/${this.maxRetries})`, {
              step: step.name,
              error: stepResult.error,
            });
            
            // 等待一段时间后重试
            await this.wait(2000);
            return await this.executeTicketingFlow(ticketConfig);
          } else {
            return { success: false, error: `步骤失败: ${step.name} - ${stepResult.error}` };
          }
        }

        // 步骤间等待
        await this.wait(config.advanced.pageLoadWait);
      }

      return { success: true, data: { message: '购票流程完成，请手动完成支付' } };

    } catch (error) {
      logger.ticketError('购票流程执行异常', { error: error.message, step: currentStep });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤1: 访问网站
   */
  async navigateToSite(ticketConfig) {
    try {
      const { page } = this.currentSession;
      const targetUrl = ticketConfig.url || config.ticket.targetUrl;

      logger.ticketInfo('正在访问目标网站', { url: targetUrl });

      await page.goto(targetUrl, {
        waitUntil: config.waitStrategies.networkIdle,
        timeout: config.app.timeout,
      });

      // 等待页面加载完成
      await page.waitForLoadState('domcontentloaded');

      // 检查页面是否正常加载
      const title = await page.title();
      logger.ticketInfo('页面加载成功', { title, url: page.url() });

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/01-homepage.png');
      }

      return { success: true };
    } catch (error) {
      logger.ticketError('访问网站失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤2: 分析页面
   */
  async analyzePage(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在分析页面结构');

      // 检查常见元素
      const pageAnalysis = await page.evaluate(() => {
        return {
          hasEventList: !!document.querySelector('.event-list, .events, .show-list'),
          hasSearchBox: !!document.querySelector('input[type="search"], .search-input'),
          hasDatePicker: !!document.querySelector('.date-picker, input[type="date"]'),
          hasLoginButton: !!document.querySelector('.login, .sign-in, .user-login, a[href*="login"]'),
          hasCartButton: !!document.querySelector('.cart, .shopping-cart'),
          hasUserInfo: !!document.querySelector('.user-info, .user-name, .logged-in'),
          title: document.title,
          url: window.location.href,
        };
      });

      logger.ticketInfo('页面分析完成', pageAnalysis);

      return { success: true, data: pageAnalysis };
    } catch (error) {
      logger.ticketError('页面分析失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤3: 检查登录状态
   */
  async checkLoginStatus(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在检查登录状态');

      // 检查登录状态
      const loginStatus = await page.evaluate(() => {
        // 检查是否有用户信息显示
        const hasUserInfo = !!document.querySelector('.user-info, .user-name, .logged-in, .user-avatar');

        // 检查是否有登录按钮
        const hasLoginButton = !!document.querySelector('.login, .sign-in, .user-login, a[href*="login"]');

        // 检查URL是否包含登录相关信息
        const urlHasLogin = window.location.href.includes('login');

        // 检查是否有登出按钮
        const hasLogoutButton = !!document.querySelector('.logout, .sign-out, a[href*="logout"]');

        return {
          isLoggedIn: hasUserInfo || hasLogoutButton,
          needsLogin: hasLoginButton && !hasUserInfo,
          isLoginPage: urlHasLogin,
          hasUserInfo,
          hasLoginButton,
          hasLogoutButton,
        };
      });

      logger.ticketInfo('登录状态检查完成', loginStatus);

      // 如果需要登录，等待用户手动登录
      if (loginStatus.needsLogin || loginStatus.isLoginPage) {
        logger.ticketInfo('⚠️ 检测到需要登录，请在浏览器中手动登录');

        // 等待用户登录
        await this.waitForUserLogin(page);
      }

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/02-login-check.png');
      }

      return { success: true, data: loginStatus };
    } catch (error) {
      logger.ticketError('登录状态检查失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 等待用户手动登录
   */
  async waitForUserLogin(page) {
    logger.ticketInfo('🔐 等待用户登录...');
    logger.ticketInfo('请在浏览器中完成登录操作，程序将自动检测登录状态');

    const maxWaitTime = 300000; // 5分钟
    const checkInterval = 3000; // 3秒检查一次
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        // 检查登录状态
        const isLoggedIn = await page.evaluate(() => {
          // 多种方式检测登录状态
          const hasUserInfo = !!document.querySelector('.user-info, .user-name, .logged-in, .user-avatar');
          const hasLogoutButton = !!document.querySelector('.logout, .sign-out, a[href*="logout"]');
          const noLoginButton = !document.querySelector('.login, .sign-in, .user-login, a[href*="login"]');

          // 检查URL变化
          const urlChanged = !window.location.href.includes('login');

          return hasUserInfo || hasLogoutButton || (noLoginButton && urlChanged);
        });

        if (isLoggedIn) {
          logger.ticketSuccess('✅ 检测到用户已登录，继续购票流程');
          return;
        }

        // 显示等待进度
        const remainingTime = Math.ceil((maxWaitTime - waitTime) / 1000);
        logger.ticketInfo(`⏳ 等待登录中... (剩余 ${remainingTime} 秒)`);

        await this.wait(checkInterval);
        waitTime += checkInterval;

      } catch (error) {
        logger.ticketError('登录状态检测异常', { error: error.message });
        await this.wait(checkInterval);
        waitTime += checkInterval;
      }
    }

    // 超时处理
    logger.ticketError('⏰ 等待登录超时，请检查登录状态');
    throw new Error('等待用户登录超时');
  }

  /**
   * 步骤4: 选择活动
   */
  async selectEvent(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在选择活动', { targetEvent: ticketConfig.eventName || '第一个活动' });

      // 等待页面完全加载
      await page.waitForLoadState('networkidle');
      await this.wait(2000);

      // 查找活动选择区域 - 更全面的选择器
      const eventSelectors = [
        config.selectors.eventList,
        '.event-list .event-item',
        '.show-list .show-item',
        '.activity-list .activity-item',
        '.event-card',
        '.show-card',
        '.event-box',
        '.show-box',
        '.ticket-event',
        '.performance-item',
        '.event-item',
        '.show-item',
        'a[href*="event"]',
        'a[href*="show"]',
        'a[href*="performance"]',
        'a[href*="ticket"]',
        '.event-title',
        '.show-title',
        '[data-event-id]',
        '[data-show-id]',
      ];

      let eventFound = false;
      let selectedEventInfo = null;

      // 首先尝试获取所有可用活动信息
      const availableEvents = await page.evaluate(() => {
        const events = [];
        const selectors = [
          '.event-list .event-item',
          '.show-list .show-item',
          '.activity-list .activity-item',
          '.event-card',
          '.show-card',
          '.event-box',
          '.show-box',
          '.ticket-event',
          '.performance-item',
          'a[href*="event"]',
          'a[href*="show"]',
          'a[href*="performance"]',
          'a[href*="ticket"]',
        ];

        selectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach((el, index) => {
            const text = el.textContent?.trim();
            const href = el.href;
            if (text && text.length > 0) {
              events.push({
                selector,
                index,
                text,
                href,
                element: el.outerHTML.substring(0, 200)
              });
            }
          });
        });

        return events;
      });

      logger.ticketInfo('发现可用活动', { count: availableEvents.length, events: availableEvents.slice(0, 5) });

      for (const selector of eventSelectors) {
        try {
          const elements = await page.locator(selector).all();

          if (elements.length === 0) continue;

          logger.ticketInfo(`尝试选择器: ${selector}`, { count: elements.length });

          if (ticketConfig.eventName) {
            // 查找指定活动 - 支持模糊匹配
            for (let i = 0; i < elements.length; i++) {
              const eventElement = elements[i];
              const eventText = await eventElement.textContent();

              if (eventText) {
                const normalizedEventText = eventText.trim().toLowerCase();
                const normalizedTargetName = ticketConfig.eventName.trim().toLowerCase();

                // 支持多种匹配方式
                const isMatch = normalizedEventText.includes(normalizedTargetName) ||
                               normalizedTargetName.includes(normalizedEventText) ||
                               this.fuzzyMatch(normalizedEventText, normalizedTargetName);

                if (isMatch) {
                  logger.ticketInfo('找到匹配活动', {
                    target: ticketConfig.eventName,
                    found: eventText.trim(),
                    selector,
                    index: i
                  });

                  // 滚动到元素可见
                  await eventElement.scrollIntoViewIfNeeded();
                  await this.wait(1000);

                  // 点击活动
                  await eventElement.click();
                  eventFound = true;
                  selectedEventInfo = { name: eventText.trim(), selector, index: i };

                  logger.ticketSuccess('已选择指定活动', selectedEventInfo);
                  break;
                }
              }
            }
          } else {
            // 选择第一个活动
            const firstElement = elements[0];
            const eventText = await firstElement.textContent();

            logger.ticketInfo('选择第一个活动', { text: eventText?.trim() });

            // 滚动到元素可见
            await firstElement.scrollIntoViewIfNeeded();
            await this.wait(1000);

            await firstElement.click();
            eventFound = true;
            selectedEventInfo = { name: eventText?.trim() || '第一个活动', selector, index: 0 };

            logger.ticketSuccess('已选择第一个活动', selectedEventInfo);
            break;
          }

          if (eventFound) break;
        } catch (e) {
          logger.ticketError(`选择器 ${selector} 执行失败`, { error: e.message });
          continue;
        }
      }

      if (!eventFound) {
        logger.ticketError('未找到可选择的活动', {
          targetEvent: ticketConfig.eventName,
          availableEvents: availableEvents.slice(0, 10)
        });
        return { success: false, error: '未找到可选择的活动，请检查活动名称或网站结构' };
      }

      // 等待页面跳转和加载
      await page.waitForLoadState('domcontentloaded');
      await this.wait(3000); // 额外等待确保页面完全加载

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/03-event-selected.png');
      }

      return { success: true, data: selectedEventInfo };
    } catch (error) {
      logger.ticketError('选择活动失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 模糊匹配函数
   */
  fuzzyMatch(str1, str2, threshold = 0.6) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length >= threshold;
  }

  /**
   * 计算编辑距离
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 步骤5: 选择票务和票价
   */
  async selectTickets(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在选择票务和票价', {
        quantity: ticketConfig.quantity || config.ticket.quantity,
        priceRange: ticketConfig.priceRange || '任意价格'
      });

      // 等待页面完全加载
      await page.waitForLoadState('networkidle');
      await this.wait(3000);

      // 第一步：选择票价
      const priceSelected = await this.selectTicketPrice(page, ticketConfig);
      if (!priceSelected.success) {
        logger.ticketWarning('票价选择失败，继续尝试其他步骤', { error: priceSelected.error });
      }

      // 第二步：设置票数
      const quantity = ticketConfig.quantity || config.ticket.quantity;
      const quantitySet = await this.setTicketQuantity(page, quantity);
      if (!quantitySet.success) {
        logger.ticketWarning('票数设置失败，使用默认值', { error: quantitySet.error });
      }

      // 第三步：选择座位（如果需要）
      const seatSelected = await this.selectSeats(page, ticketConfig);
      if (!seatSelected.success) {
        logger.ticketWarning('座位选择失败，可能是自由座位', { error: seatSelected.error });
      }

      // 第四步：点击购买/选择按钮
      const buttonClicked = await this.clickPurchaseButton(page);
      if (!buttonClicked.success) {
        return { success: false, error: '未找到购买按钮，请检查页面结构' };
      }

      // 等待页面跳转
      await page.waitForLoadState('domcontentloaded');
      await this.wait(2000);

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/04-tickets-selected.png');
      }

      return {
        success: true,
        data: {
          priceSelected: priceSelected.success,
          quantitySet: quantitySet.success,
          seatSelected: seatSelected.success,
          quantity: quantity
        }
      };
    } catch (error) {
      logger.ticketError('选择票务失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 选择票价
   */
  async selectTicketPrice(page, ticketConfig) {
    try {
      logger.ticketInfo('正在选择票价');

      // 查找票价选择区域
      const priceSelectors = [
        '.price-list .price-item',
        '.ticket-price .price-option',
        '.price-selection .price',
        '.seat-price',
        '.ticket-type',
        '.price-category',
        '[data-price]',
        '.price-box',
        '.ticket-price-item',
        'input[type="radio"][name*="price"]',
        'button[data-price]',
        '.price-btn',
      ];

      // 获取所有可用票价
      const availablePrices = await page.evaluate(() => {
        const prices = [];
        const selectors = [
          '.price-list .price-item',
          '.ticket-price .price-option',
          '.price-selection .price',
          '.seat-price',
          '.ticket-type',
          '.price-category',
          '[data-price]',
          '.price-box',
          '.ticket-price-item',
          'input[type="radio"][name*="price"]',
          'button[data-price]',
          '.price-btn',
        ];

        selectors.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach((el, index) => {
            const text = el.textContent?.trim();
            const priceMatch = text?.match(/\d+/);
            const price = priceMatch ? parseInt(priceMatch[0]) : null;

            if (text && text.length > 0) {
              prices.push({
                selector,
                index,
                text,
                price,
                element: el.outerHTML.substring(0, 200)
              });
            }
          });
        });

        return prices;
      });

      logger.ticketInfo('发现可用票价', { count: availablePrices.length, prices: availablePrices.slice(0, 5) });

      let priceSelected = false;
      let selectedPriceInfo = null;

      for (const selector of priceSelectors) {
        try {
          const elements = await page.locator(selector).all();

          if (elements.length === 0) continue;

          logger.ticketInfo(`尝试票价选择器: ${selector}`, { count: elements.length });

          // 根据配置选择票价
          if (ticketConfig.priceRange) {
            // 如果指定了价格范围
            for (let i = 0; i < elements.length; i++) {
              const priceElement = elements[i];
              const priceText = await priceElement.textContent();

              if (priceText) {
                const priceMatch = priceText.match(/\d+/);
                const price = priceMatch ? parseInt(priceMatch[0]) : null;

                if (price && this.isPriceInRange(price, ticketConfig.priceRange)) {
                  logger.ticketInfo('找到匹配价格', {
                    target: ticketConfig.priceRange,
                    found: priceText.trim(),
                    price: price
                  });

                  await priceElement.scrollIntoViewIfNeeded();
                  await this.wait(1000);
                  await priceElement.click();

                  priceSelected = true;
                  selectedPriceInfo = { text: priceText.trim(), price, selector, index: i };
                  logger.ticketSuccess('已选择指定价格', selectedPriceInfo);
                  break;
                }
              }
            }
          } else {
            // 选择第一个可用票价
            const firstElement = elements[0];
            const priceText = await firstElement.textContent();

            logger.ticketInfo('选择第一个票价', { text: priceText?.trim() });

            await firstElement.scrollIntoViewIfNeeded();
            await this.wait(1000);
            await firstElement.click();

            priceSelected = true;
            selectedPriceInfo = { text: priceText?.trim() || '第一个票价', selector, index: 0 };
            logger.ticketSuccess('已选择第一个票价', selectedPriceInfo);
            break;
          }

          if (priceSelected) break;
        } catch (e) {
          logger.ticketError(`票价选择器 ${selector} 执行失败`, { error: e.message });
          continue;
        }
      }

      if (!priceSelected) {
        return { success: false, error: '未找到可选择的票价' };
      }

      await this.wait(1000);
      return { success: true, data: selectedPriceInfo };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 判断价格是否在指定范围内
   */
  isPriceInRange(price, priceRange) {
    if (typeof priceRange === 'string') {
      // 解析价格范围字符串，如 "100-500", ">=200", "<=300"
      if (priceRange.includes('-')) {
        const [min, max] = priceRange.split('-').map(p => parseInt(p.trim()));
        return price >= min && price <= max;
      } else if (priceRange.startsWith('>=')) {
        const min = parseInt(priceRange.substring(2).trim());
        return price >= min;
      } else if (priceRange.startsWith('<=')) {
        const max = parseInt(priceRange.substring(2).trim());
        return price <= max;
      } else if (priceRange.startsWith('>')) {
        const min = parseInt(priceRange.substring(1).trim());
        return price > min;
      } else if (priceRange.startsWith('<')) {
        const max = parseInt(priceRange.substring(1).trim());
        return price < max;
      } else {
        // 精确匹配
        const targetPrice = parseInt(priceRange);
        return price === targetPrice;
      }
    } else if (typeof priceRange === 'object') {
      // 对象格式 { min: 100, max: 500 }
      const min = priceRange.min || 0;
      const max = priceRange.max || Infinity;
      return price >= min && price <= max;
    }

    return false;
  }

  /**
   * 设置票数
   */
  async setTicketQuantity(page, quantity) {
    try {
      logger.ticketInfo('正在设置票数', { quantity });

      // 尝试不同的票数设置方式
      const quantityMethods = [
        // 方法1: 直接输入框
        async () => {
          const selectors = [
            config.selectors?.quantityInput,
            'input[name*="quantity"]',
            'input[name*="num"]',
            'input[name*="count"]',
            'input[type="number"]',
            '.quantity-input',
            '.ticket-quantity input',
            '#quantity',
            '#ticketNum',
          ].filter(Boolean);

          for (const selector of selectors) {
            try {
              const quantityInput = page.locator(selector).first();
              if (await quantityInput.count() > 0 && await quantityInput.isVisible()) {
                await quantityInput.clear();
                await quantityInput.fill(quantity.toString());
                logger.ticketInfo('通过输入框设置票数成功', { selector, quantity });
                return true;
              }
            } catch (e) {
              continue;
            }
          }
          return false;
        },

        // 方法2: 点击增加按钮
        async () => {
          const selectors = [
            config.selectors?.quantityIncrease,
            '.quantity-plus',
            '.quantity-add',
            '.btn-plus',
            '.increase-btn',
            'button[data-action="increase"]',
            '.quantity-control .plus',
          ].filter(Boolean);

          for (const selector of selectors) {
            try {
              const increaseBtn = page.locator(selector).first();
              if (await increaseBtn.count() > 0 && await increaseBtn.isVisible()) {
                // 先重置到1
                const decreaseBtn = page.locator(selector.replace('plus', 'minus').replace('increase', 'decrease').replace('add', 'sub')).first();
                if (await decreaseBtn.count() > 0) {
                  for (let i = 0; i < 10; i++) { // 最多点击10次减少按钮
                    try {
                      await decreaseBtn.click();
                      await this.wait(200);
                    } catch (e) {
                      break;
                    }
                  }
                }

                // 然后增加到目标数量
                for (let i = 1; i < quantity; i++) {
                  await increaseBtn.click();
                  await this.wait(500);
                }
                logger.ticketInfo('通过增加按钮设置票数成功', { selector, quantity });
                return true;
              }
            } catch (e) {
              continue;
            }
          }
          return false;
        },

        // 方法3: 下拉选择
        async () => {
          const selectors = [
            'select[name*="quantity"]',
            'select[name*="num"]',
            'select[name*="count"]',
            '.quantity-select',
            '.ticket-quantity select',
          ];

          for (const selector of selectors) {
            try {
              const selectElement = page.locator(selector).first();
              if (await selectElement.count() > 0 && await selectElement.isVisible()) {
                await selectElement.selectOption(quantity.toString());
                logger.ticketInfo('通过下拉选择设置票数成功', { selector, quantity });
                return true;
              }
            } catch (e) {
              continue;
            }
          }
          return false;
        },

        // 方法4: 点击数字按钮
        async () => {
          const quantityButtons = await page.locator(`button:has-text("${quantity}"), .quantity-btn:has-text("${quantity}")`).all();
          if (quantityButtons.length > 0) {
            await quantityButtons[0].click();
            logger.ticketInfo('通过数字按钮设置票数成功', { quantity });
            return true;
          }
          return false;
        },
      ];

      for (const method of quantityMethods) {
        try {
          if (await method()) {
            await this.wait(1000);
            return { success: true, quantity };
          }
        } catch (e) {
          continue;
        }
      }

      return { success: false, error: '未找到票数设置选项' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 选择座位
   */
  async selectSeats(page, ticketConfig) {
    try {
      logger.ticketInfo('正在选择座位');

      // 查找座位选择区域
      const seatSelectors = [
        '.seat-map .seat',
        '.seat-selection .seat',
        '.seat-chart .seat',
        '.available-seat',
        '.seat.available',
        '[data-seat-id]',
        '.seat-item',
        '.seat-box',
      ];

      let seatSelected = false;
      const quantity = ticketConfig.quantity || config.ticket.quantity;

      for (const selector of seatSelectors) {
        try {
          const availableSeats = await page.locator(selector).filter({ hasNotClass: 'occupied' }).filter({ hasNotClass: 'disabled' }).all();

          if (availableSeats.length === 0) continue;

          logger.ticketInfo(`找到可用座位`, { selector, count: availableSeats.length });

          // 选择指定数量的座位
          const seatsToSelect = Math.min(quantity, availableSeats.length);

          for (let i = 0; i < seatsToSelect; i++) {
            await availableSeats[i].click();
            await this.wait(500);
          }

          seatSelected = true;
          logger.ticketSuccess('座位选择成功', { selected: seatsToSelect });
          break;
        } catch (e) {
          continue;
        }
      }

      if (!seatSelected) {
        return { success: false, error: '未找到可选择的座位' };
      }

      await this.wait(1000);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 点击购买按钮
   */
  async clickPurchaseButton(page) {
    try {
      logger.ticketInfo('正在查找购买按钮');

      // 查找购买按钮
      const buttonSelectors = [
        config.selectors?.selectButton,
        '.buy-now',
        '.purchase-btn',
        '.add-to-cart',
        '.book-now',
        '.confirm-btn',
        '.next-btn',
        '.continue-btn',
        'button:has-text("购买")',
        'button:has-text("立即购买")',
        'button:has-text("选择")',
        'button:has-text("确认")',
        'button:has-text("下一步")',
        'button:has-text("继续")',
        'button:has-text("Buy")',
        'button:has-text("Purchase")',
        'button:has-text("Select")',
        'button:has-text("Confirm")',
        'input[type="submit"]',
        '[data-action="purchase"]',
        '[data-action="buy"]',
      ].filter(Boolean);

      for (const selector of buttonSelectors) {
        try {
          const button = page.locator(selector).first();
          if (await button.count() > 0 && await button.isVisible() && await button.isEnabled()) {
            logger.ticketInfo('找到购买按钮', { selector });

            await button.scrollIntoViewIfNeeded();
            await this.wait(1000);
            await button.click();

            logger.ticketSuccess('购买按钮点击成功');
            return { success: true, selector };
          }
        } catch (e) {
          continue;
        }
      }

      return { success: false, error: '未找到可点击的购买按钮' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤6: 填写用户信息
   */
  async fillUserInfo(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在填写用户信息');

      const userInfo = {
        ...config.ticket.userInfo,
        ...ticketConfig.userInfo,
      };

      // 填写姓名
      if (userInfo.name) {
        try {
          const nameInput = page.locator(config.selectors.userNameInput).first();
          if (await nameInput.count() > 0) {
            await nameInput.fill(userInfo.name);
            logger.ticketInfo('姓名填写完成');
          }
        } catch (e) {
          logger.ticketInfo('姓名输入框未找到');
        }
      }

      // 填写邮箱
      if (userInfo.email) {
        try {
          const emailInput = page.locator(config.selectors.userEmailInput).first();
          if (await emailInput.count() > 0) {
            await emailInput.fill(userInfo.email);
            logger.ticketInfo('邮箱填写完成');
          }
        } catch (e) {
          logger.ticketInfo('邮箱输入框未找到');
        }
      }

      // 填写电话
      if (userInfo.phone) {
        try {
          const phoneInput = page.locator(config.selectors.userPhoneInput).first();
          if (await phoneInput.count() > 0) {
            await phoneInput.fill(userInfo.phone);
            logger.ticketInfo('电话填写完成');
          }
        } catch (e) {
          logger.ticketInfo('电话输入框未找到');
        }
      }

      // 保存截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/05-info-filled.png');
      }

      return { success: true };
    } catch (error) {
      logger.ticketError('填写用户信息失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 步骤7: 确认订单
   */
  async confirmOrder(ticketConfig) {
    try {
      const { page } = this.currentSession;

      logger.ticketInfo('正在确认订单');

      // 查找确认按钮
      const confirmButtons = [
        config.selectors.submitButton,
        '.confirm-order',
        '.proceed-to-payment',
        'button:has-text("确认")',
        'button:has-text("提交")',
        'button:has-text("下一步")',
      ];

      let confirmClicked = false;
      for (const selector of confirmButtons) {
        try {
          const button = page.locator(selector).first();
          if (await button.count() > 0 && await button.isVisible()) {
            await button.click();
            confirmClicked = true;
            logger.ticketInfo('已点击确认按钮');
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!confirmClicked) {
        logger.ticketInfo('未找到确认按钮，可能已到达支付页面');
      }

      // 等待页面响应
      await page.waitForLoadState('domcontentloaded');

      // 检查是否到达支付页面
      const isPaymentPage = await page.evaluate(() => {
        const url = window.location.href.toLowerCase();
        const content = document.body.textContent.toLowerCase();
        return url.includes('payment') || 
               url.includes('checkout') || 
               content.includes('支付') || 
               content.includes('payment') ||
               content.includes('checkout');
      });

      if (isPaymentPage) {
        logger.ticketSuccess('已到达支付页面，请手动完成支付');
      }

      // 保存最终截图
      if (config.advanced.saveScreenshots) {
        await this.browserManager.takeScreenshot(this.currentSession.pageId, 'screenshots/06-order-confirmed.png');
      }

      return { success: true, data: { paymentRequired: isPaymentPage } };
    } catch (error) {
      logger.ticketError('确认订单失败', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 等待指定时间
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 停止购票流程
   */
  async stop() {
    if (!this.isRunning) {
      return;
    }

    logger.ticketInfo('正在停止购票流程');
    this.isRunning = false;
    await this.cleanup();
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.currentSession) {
        await this.browserManager.closePage(this.currentSession.pageId);
        await this.browserManager.closeContext(this.currentSession.contextId);
        this.currentSession = null;
      }

      await this.browserManager.cleanup();
      logger.ticketInfo('购票资源清理完成');
    } catch (error) {
      logger.ticketError('资源清理失败', { error: error.message });
    }
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      retryCount: this.retryCount,
      maxRetries: this.maxRetries,
      startTime: this.startTime,
      duration: this.startTime ? Date.now() - this.startTime : 0,
      currentSession: this.currentSession ? {
        pageId: this.currentSession.pageId,
        contextId: this.currentSession.contextId,
        startTime: this.currentSession.startTime,
      } : null,
      browserStatus: this.browserManager.getStatus(),
    };
  }
}

export default TicketBot;
